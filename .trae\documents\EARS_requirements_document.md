# 荆门市医保数据联动应用信息系统需求文档

## 引言

本文档描述了荆门市医保数据联动应用信息系统的功能需求。该系统旨在建设一个荆门市医保局与其他政府机关、医药机构进行工单联动处理的统一平台，涵盖死亡人员数据核查、意外伤害报销核查、住院病患外出核查、病患投诉举报核查、执业药师在岗核查、职工医保征缴投诉处理和移送问题线索等7个核心业务场景。系统通过标准化的工单流程，实现跨部门协作和数据核查的自动化管理。

## 需求

### 需求1：死亡人员数据核查工单管理

**用户故事:** 作为市医保局工作人员，我想要接收大数据局提供的死亡人员数据并分派给县医保局核实，以便及时处理死亡人员的医保相关事务。

#### 验收标准

1. 当大数据局上传死亡人员数据时，那么系统应自动生成死亡人员核查工单
2. 当市医保局选择县医保局作为承办方时，那么系统应将工单发送给指定的县医保局
3. 当县医保局登录系统时，那么系统应显示待处理的死亡人员核查工单列表
4. 当县医保局完成核实工作时，那么系统应允许上传核实结果和相关证明材料
5. 当县医保局提交处理结果时，那么系统应将工单状态更新为"处理完毕"
6. 当市医保局确认处理结果时，那么系统应将工单状态更新为"已归档"
7. 当核实结果不符合要求时，那么系统应允许市医保局要求县医保局补充办理

### 需求2：意外伤害报销数据核查工单管理

**用户故事:** 作为市医保局工作人员，我想要发起意外伤害报销数据核查工单，以便公安、法院、检察院等执法机关能够核实意外伤害医保报销的合法性。

#### 验收标准

1. 当市医保局选择"意外伤害报销核查"工单类型时，那么系统应显示意外伤害报销核查专用表单
2. 当市医保局上传意外伤害报销相关附件时，那么系统应支持Excel、Word、PDF和图片格式文件
3. 当市医保局提交意外伤害核查工单时，那么系统应将工单发送给指定的执法机关
4. 当执法机关登录系统时，那么系统应显示待处理的意外伤害核查工单
5. 当执法机关提交处理结果时，那么系统应允许上传案件核实证明等附件
6. 当处理结果不符合要求时，那么系统应允许市医保局要求承办方补充办理

### 需求3：住院病患外出信息核查工单管理

**用户故事:** 作为市医保局工作人员，我想要发起住院病患异常外出核实工单，以便公安部门能够通过"天网"系统核实住院人员外出情况。

#### 验收标准

1. 当市医保局选择"住院病患外出核查"工单类型时，那么系统应显示住院病患外出核查表单
2. 当市医保局提交住院病患外出核查工单时，那么系统应将工单发送给公安部门
3. 当公安部门收到核查工单时，那么系统应提供住院人员基本信息和住院时间段
4. 当公安部门通过"天网"系统完成外出行为核实后，那么系统应允许上传核实结果和相关证据
5. 当核实结果显示存在异常外出时，那么系统应标记该工单为异常并通知市医保局
6. 当核实过程需要补充信息时，那么系统应支持承办方与发起方的信息交互

### 需求4：病患投诉举报信息核查工单管理

**用户故事:** 作为市医保局工作人员，我想要发起病患投诉举报信息核查工单，以便相关医院能够及时核实并处理患者投诉举报。

#### 验收标准

1. 当市医保局选择"病患投诉举报核查"工单类型时，那么系统应显示病患投诉举报核查表单
2. 当市医保局选择被投诉医院作为承办方时，那么系统应自动匹配对应医院信息
3. 当医院收到投诉举报核查工单时，那么系统应提供完整的投诉信息和患者信息
4. 当医院完成投诉核实后，那么系统应允许上传处理结果和相关证明材料
5. 当投诉核实需要补充信息时，那么系统应支持医院与市医保局的信息交互
6. 当投诉核实完成后，那么系统应自动更新工单状态并通知市医保局

### 需求5：执业药师在岗情况核查工单管理

**用户故事:** 作为市医保局工作人员，我想要发起执业药师在岗情况核查工单，以便相关药店能够证明药师在岗情况并上传佐证材料。

#### 验收标准

1. 当市医保局选择"执业药师在岗核查"工单类型时，那么系统应显示执业药师在岗核查表单
2. 当市医保局提交执业药师核查工单时，那么系统应将工单发送给相关药店
3. 当药店收到核查工单时，那么系统应明确显示需要证明的药师信息和在岗要求
4. 当药店上传佐证材料时，那么系统应支持劳务合同、社保缴费证明等多种文件格式
5. 当药店提交在岗证明后，那么系统应自动验证材料完整性并更新工单状态
6. 当证明材料不符合要求时，那么系统应通知药店补充或重新上传

### 需求6：职工医保征缴投诉案件处理工单管理

**用户故事:** 作为市医保局工作人员，我想要发起职工医保征缴投诉案件处理工单，以便税务部门能够完成企业医保催缴工作。

#### 验收标准

1. 当市医保局选择"职工医保征缴投诉"工单类型时，那么系统应显示医保征缴投诉处理表单
2. 当市医保局提交征缴投诉工单时，那么系统应将工单发送给税务部门
3. 当税务部门收到征缴投诉工单时，那么系统应提供企业信息和投诉详情
4. 当税务部门完成催缴工作后，那么系统应允许上传催缴结果和相关证明
5. 当企业完成医保补缴后，那么系统应自动更新工单状态为"处理完成"
6. 当催缴工作遇到困难时，那么系统应支持税务部门与市医保局的沟通协调

### 需求7：移送问题线索工单管理

**用户故事:** 作为政府机关工作人员，我想要发起移送问题线索工单，以便承办方能够及时处理并回复处理结果。

#### 验收标准

1. 当政府机关选择"移送问题线索"工单类型时，那么系统应显示问题线索移送表单
2. 当政府机关填写线索信息并选择承办方时，那么系统应验证承办方身份和权限
3. 当政府机关提交移送工单时，那么系统应生成唯一的线索编号
4. 当承办方收到移送线索时，那么系统应提供完整的线索信息和处理要求
5. 当承办方完成线索处理后，那么系统应允许上传处理结果和相关材料
6. 当处理结果需要复核时，那么系统应支持发起方要求补充说明或重新处理

### 需求8：工单创建与提交功能

**用户故事:** 作为系统用户，我想要创建和提交工单，以便启动跨部门的数据核查和问题处理流程。

#### 验收标准

1. 当用户登录系统后，那么系统应显示可用的工单类型列表
2. 当用户选择工单类型时，那么系统应显示对应的表单模板
3. 当用户填写工单信息时，那么系统应进行必填项验证
4. 当用户上传附件时，那么系统应验证文件格式和大小
5. 当用户选择承办方时，那么系统应提供可选择的承办方列表
6. 当用户点击提交按钮时，那么系统应生成唯一的工单编号并发送给承办方
7. 当用户选择"提交并复制工单"时，那么系统应复制除承办方外的所有内容到新工单
8. 当工单编号重复时，那么系统应自动生成新的唯一编号
9. 当用户提交工单时，那么系统应验证所有必填字段的完整性

### 需求9：工单处理与反馈功能

**用户故事:** 作为承办方用户，我想要处理收到的工单并反馈结果，以便完成数据核查和问题处理。

#### 验收标准

1. 当承办方登录系统时，那么系统应显示待处理的工单列表
2. 当承办方点击工单时，那么系统应显示完整的工单信息和附件
3. 当承办方处理工单时，那么系统应提供处理结果录入界面
4. 当承办方上传处理结果附件时，那么系统应支持多种文件格式
5. 当承办方提交处理结果时，那么系统应将工单状态更新为"处理完成"
6. 当处理结果提交后，那么系统应自动通知发起方
7. 当处理结果提交后，那么系统应自动通知发起方有新的处理结果

### 需求10：工单状态管理

**用户故事:** 作为系统用户，我想要跟踪和管理工单状态，以便了解工单处理的进展情况。

#### 验收标准

1. 当工单状态发生变化时，那么系统应自动更新状态记录
2. 当用户查看工单时，那么系统应显示当前状态和历史状态变化
3. 当工单超时未处理时，那么系统应自动发送提醒通知
4. 当用户需要查询工单时，那么系统应支持按状态筛选
5. 当工单状态为"处理完成"时，那么系统应显示处理结果和附件
6. 当工单需要重新处理时，那么系统应支持状态回退功能
7. 当工单状态变更时，那么系统应记录状态变更时间和操作人员信息

### 需求11：用户权限与访问控制

**用户故事:** 作为系统管理员，我想要管理不同用户的访问权限，以便确保数据安全和操作合规。

#### 验收标准

1. 当管理员创建新用户时，那么系统应允许分配角色和权限
2. 当用户登录系统时，那么系统应根据角色显示对应的功能菜单
3. 当用户尝试访问受限功能时，那么系统应验证用户权限
4. 当用户权限需要变更时，那么管理员应能够修改用户权限
5. 当用户权限变更后，那么系统应立即生效并记录变更日志
6. 当用户进行敏感操作时，那么系统应记录操作日志和审计信息
7. 当用户会话超时时，那么系统应自动注销并要求重新登录
8. 当用户尝试越权操作时，那么系统应拒绝访问并记录操作日志

### 需求12：系统通知与消息提醒

**用户故事:** 作为系统用户，我想要及时收到工单相关的通知提醒，以便及时处理待办事项。

#### 验收标准

1. 当工单被分配给承办方时，那么系统应立即发送通知给承办方
2. 当工单状态发生变化时，那么系统应通知相关用户
3. 当工单即将超时时，那么系统应发送提醒通知
4. 当用户收到通知时，那么系统应提供快速访问工单的链接
5. 当通知发送失败时，那么系统应记录失败日志并尝试重发
6. 当用户查看通知后，那么系统应将通知标记为已读
7. 当系统发送通知失败时，那么系统应重试发送并记录失

