## 荆门市“医保数据联动应用信息系统”

### 一、系统概述

建设一个荆门市医保局与其他政府机关、医药机构进行工单联动处理的系统平台。
任务发起方上传的文件不仅是单个待核实的医患病人，大部分情况是EXCEL文件，包含多个患者的信息（姓名、身份证号码、手机号、地址等）。
承办方在处理文件时，可以在待办列表页面，通过查询某个身份证号码，快速定位到某个患者，进行核实处理。


目前规划了7个业务场景：

1、死亡人员数据核查
由大数据局定期提供死亡人员数据，市医保局将任务分派给县医保局核实，县医保局处理完毕后在系统上确认处理结果。

2、意外伤害报销数据核查
市医保局发起意外伤害已报销数据核查工单。
由公安部门、法院、检察院等执法机关核实这些意外伤害医保报销人员是否有相关案件发生，并给出相关处理结果。

3、住院病患外出信息核查
市医保局发起住院病患异常外出核实工单。
由公安部门对比“天网”信息，核实住院人员在记录住院期间是否有异常外出行为，并给出相关处理结果。

4、病患投诉举报信息核查
市医保局发起患者针对医院的举报投诉信息核查工单。
由医院核实处理举报投诉案件，并给出相关处理结果。

5、执业药师在岗情况核查
市医保局发起执业药师在岗情况核查工单。
由执业药师登记注册的相关药店上传证明药师在岗的佐证材料（劳务合同、社保缴费证明等资料），并给出相关处理结果。

6、职工医保征缴投诉案件处理
市医保局发起职工医保投诉案件征缴处理工单。（医保局提供职工应参保登记、缴费基数等信息）
由税务部门对相关被投诉的企业完成催缴，并给出处理结果。

7、移送问题线索
市医保局与其他政府机关之间互相移送线索。
发起方把问题线索移送给承办方，承办方处理之后回复处理结果。

### 二、功能约束

#### 1、所有工单都在“医保数据联动应用信息系统”上处理。

#### 2、发起工单的表单内容：

（1）工单编号
（2）工单标题
（3）工单类型：单选，（死亡人员数据核查、意外伤害报销核查、住院病患外出核查、病患投诉举报核查、执业药师在岗核查、职工医保征缴投诉、移送问题线索）
（4）工单内容：富文本
（5）附件：excel、word、pdf、各种图片可预览
（6）发起方：当前登录工号部门，不可编辑
（7）承办方：单选（市公安、市检察院、市法院、XX医院、XX药店）
（8）操作按钮：

- 提交工单：提交当前表单
- 提交并复制工单：提交当前表单，并且复制所有内容，除了“承办方”。让用户自己调整新工单的附件、工单内容

#### 3、承办方工单的表单内容：

（1）原始工单只读表单
（2）承办处理结果：富文本
（3）附件：excel、word、pdf、各种图片可预览
（4）操作按钮：

- 提交工单：提交当前表单

#### 4、工单处理状态：

（1）待处理：发起方发起工单，或者发起方要求承办方补充办理结果。
（2）处理完毕：承办方完成办理
（3）已归档：发起方确认办理结果
（4）已撤回：发起方撤回工单

#### 5、所有场景任务的处理都可以上传多个文档附件和截图。