# 需求文档

## 引言

本需求文档旨在为“医保数据联动应用信息系统”的功能开发提供明确、无歧义的需求说明。该系统旨在促进荆门市医保局与其他政府机关、医药机构之间的工单联动处理，提高跨部门协作效率和数据核查准确性。本文档采用用户故事和EARS（简易需求语法方法）格式，详细描述了系统的各项功能需求和验收标准，为后续的设计、开发和测试工作提供依据。

# 需求

### 需求 1：死亡人员数据核查

**用户故事:** 作为市医保局工作人员，我想要系统能够接收大数据局提供的死亡人员数据，并将核查任务分派给县医保局，以便及时核实并更新医保系统中参保人的状态。

#### 验收标准

1.  **当** 大数据局提供死亡人员数据时，**那么** 系统应能接收并解析数据。
2.  **如果** 死亡人员数据成功接收，**那么** 系统应允许市医保局工作人员创建“死亡人员数据核查”工单。
3.  **当** 市医保局工作人员创建核查工单并选择县医保局作为承办方后，**那么** 系统应将工单分派给指定的县医保局。
4.  **当** 县医保局工作人员处理完核查任务后，**那么** 系统应允许其在工单上确认处理结果并提交。
5.  **如果** 工单状态为“处理完毕”，**那么** 市医保局工作人员应能查看县医保局提交的处理结果。

### 需求 2：意外伤害报销数据核查

**用户故事:** 作为市医保局工作人员，我想要发起对意外伤害医保报销人员的核查，以便确认相关报销是否存在欺诈、滥用或涉及第三方责任。

#### 验收标准

1.  **当** 市医保局工作人员需要核查意外伤害报销数据时，**那么** 系统应允许其创建“意外伤害报销核查”工单。
2.  **当** 创建工单时，**那么** 系统应允许工作人员选择公安、法院、检察院等执法机关作为承办方。
3.  **当** 执法机关工作人员接收到工单后，**那么** 系统应能展示工单详情，并允许其提交核查结果。
4.  **如果** 执法机关在核查中发现相关案件，**那么** 系统应允许其在处理结果中说明案件情况。

### 需求 3：住院病患外出信息核查

**用户故事:** 作为市医保局工作人员，我想要核实住院病患在住院期间是否有异常外出行为，以便发现并处理挂床住院等违规行为。

#### 验收标准

1.  **当** 市医保局怀疑存在挂床住院行为时，**那么** 系统应允许其创建“住院病患外出核查”工单。
2.  **当** 创建工单时，**那么** 系统应允许工作人员选择公安部门作为承办方。
3.  **当** 公安部门接收到工单后，**那么** 系统应允许其访问“天网”信息进行比对，并提交核查结果。
4.  **如果** 核查发现病患有异常外出行为，**那么** 系统应记录公安部门提供的处理结果和证据。

### 需求 4：病患投诉举报信息核查

**用户故事:** 作为市医保局工作人员，我想要将患者对医院的投诉举报转交给相关医院进行核实处理，以便及时解决医患纠纷，提升医疗服务质量。

#### 验收标准

1.  **当** 市医保局收到患者的投诉举报时，**那么** 系统应允许其创建“病患投诉举报核查”工单。
2.  **当** 创建工单时，**那么** 系统应允许工作人员选择被投诉的医院作为承办方。
3.  **当** 医院接收到工单后，**那么** 系统应允许其提交投诉处理过程和最终结果。
4.  **如果** 医院需要上传相关证明材料，**那么** 系统应支持附件上传功能。

### 需求 5：执业药师在岗情况核查

**用户故事:** 作为市医保局工作人员，我想要核查定点药店的执业药师是否在岗履职，以便加强对定点药店的监管。

#### 验收标准

1.  **当** 市医保局需要进行执业药师在岗情况抽查时，**那么** 系统应允许其创建“执业药师在岗核查”工单。
2.  **当** 创建工单时，**那么** 系统应允许工作人员选择相关药店作为承办方。
3.  **当** 药店接收到工单后，**那么** 系统应允许其上传执业药师在岗的证明材料（如劳务合同、社保缴费证明等）。
4.  **如果** 药店未能在规定时间内提交证明材料，**那么** 系统应将工单标记为“待处理”并提醒市医保局。

### 需求 6：职工医保征缴投诉案件处理

**用户故事:** 作为市医保局工作人员，我想要将涉及职工医保征缴的投诉案件移交给税务部门处理，以便追缴欠缴的医保费用，保障职工权益。

#### 验收标准

1.  **当** 市医保局收到职工医保征缴相关的投诉时，**那么** 系统应允许其创建“职工医保征缴投诉”工单。
2.  **当** 创建工单时，**那么** 系统应允许工作人员录入职工应参保登记、缴费基数等信息，并选择税务部门作为承办方。
3.  **当** 税务部门接收到工单后，**那么** 系统应允许其查看投诉详情并提交催缴处理结果。
4.  **如果** 税务部门成功完成催缴，**那么** 系统应记录处理结果和金额。

### 需求 7：移送问题线索

**用户故事:** 作为政府机关工作人员，我想要与其他部门互相移送问题线索，以便进行跨部门的协同办案和信息共享。

#### 验收标准

1.  **当** 任何一方（市医保局或其他政府机关）发现需要移送的问题线索时，**那么** 系统应允许其创建“移送问题线索”工单。
2.  **当** 创建工单时，**那么** 系统应允许发起方选择一个或多个承办方。
3.  **当** 承办方处理完问题线索后，**那么** 系统应允许其回复处理结果给发起方。
4.  **如果** 发起方对处理结果不满意，**那么** 系统应允许其将工单退回给承办方要求补充办理。

### 需求 8：工单管理

**用户故事:** 作为系统用户，我想要一个功能完善的工单系统来创建、提交、处理和跟踪所有业务场景的任务，以便高效地完成跨部门协作。

#### 验收标准

1.  **当** 用户创建工单时，**那么** 系统应自动生成唯一的“工单编号”。
2.  **当** 用户创建工单时，**那么** 系统应提供“工单类型”下拉选项，包含所有已定义的业务场景。
3.  **当** 用户填写工单时，**那么** 系统应提供富文本编辑器用于输入“工单内容”。
4.  **当** 用户需要上传文件时，**那么** 系统应支持 `excel`、`word`、`pdf` 和多种图片格式的附件，并提供预览功能。
5.  **如果** 用户是工单发起方，**那么** “发起方”字段应自动填充为当前用户所在部门且不可编辑。
6.  **当** 用户提交工单时，**那么** 系统应提供“提交工单”和“提交并复制工单”两个操作按钮。
7.  **当** 承办方处理工单时，**那么** 系统应以只读形式展示原始工单信息。
8.  **当** 工单被创建时，**那么** 其初始状态应为“待处理”。
9.  **当** 承办方提交处理结果后，**那么** 工单状态应变为“处理完毕”。
10. **当** 发起方确认承办方的处理结果后，**那么** 工单状态应变为“已归档”。
11. **当** 发起方在承办方处理前撤回工单，**那么** 工单状态应变为“已撤回”。