<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>云梦古今法律问答 - 历史成绩</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    html, body { width: 375px; height: 812px; margin: 0; padding: 0; background: #f6f6f6; overflow: hidden; }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 375px; height: 812px; border-radius: 40px; border: 8px solid #000; box-sizing: border-box; margin: 0 auto; background: #f6f6f6; }
    .statusbar { height: 44px; background: transparent; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; }
  </style>
</head>
<body>
  <div class="mockup relative">
    <!-- 状态栏 -->
    <div class="statusbar flex items-center justify-between">
      <span class="text-xs text-gray-700 font-mono">9:41</span>
      <div class="flex items-center space-x-1">
        <svg width="18" height="18" fill="none"><rect x="1" y="7" width="2" height="6" rx="1" fill="#222"/><rect x="5" y="5" width="2" height="8" rx="1" fill="#222"/><rect x="9" y="3" width="2" height="10" rx="1" fill="#222"/><rect x="13" y="1" width="2" height="12" rx="1" fill="#222"/></svg>
        <svg width="18" height="18" fill="none"><circle cx="9" cy="9" r="7" stroke="#222" stroke-width="2"/></svg>
      </div>
    </div>
    <!-- 内容区 -->
    <div class="flex flex-col px-6 pt-4 pb-4 h-[768px]">
      <h2 class="text-xl font-bold text-gray-800 mb-4 text-center">历史成绩</h2>
      <div class="bg-white rounded-2xl shadow p-4 mb-4">
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-gray-400">2024-06-01</span>
          <span class="flex-1 text-gray-800 font-semibold ml-2">4/5分</span>
          <button class="text-blue-400 text-sm ml-2">查看解析</button>
        </div>
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-gray-400">2024-05-30</span>
          <span class="flex-1 text-gray-800 font-semibold ml-2">5/5分</span>
          <button class="text-blue-400 text-sm ml-2">查看解析</button>
        </div>
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-gray-400">2024-05-28</span>
          <span class="flex-1 text-gray-800 font-semibold ml-2">3/5分</span>
          <button class="text-blue-400 text-sm ml-2">查看解析</button>
        </div>
        <!-- ...更多历史成绩... -->
      </div>
      <div class="mt-auto flex justify-center w-full">
        <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=80&q=80" alt="青铜器" class="w-10 h-10 rounded-full object-cover border-2 border-green-300 shadow-md">
      </div>
    </div>
  </div>
</body>
</html> 