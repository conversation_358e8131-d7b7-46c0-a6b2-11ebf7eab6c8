<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>云梦古今法律问答 - 首页</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    html, body { width: 375px; height: 812px; margin: 0; padding: 0; background: #f6f6f6; overflow: hidden; }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 375px; height: 812px; border-radius: 40px; border: 8px solid #000; box-sizing: border-box; margin: 0 auto; background: #f6f6f6; }
    .statusbar { height: 44px; background: transparent; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; }
  </style>
</head>
<body>
  <div class="mockup relative">
    <!-- 状态栏 -->
    <div class="statusbar flex items-center justify-between">
      <span class="text-xs text-gray-700 font-mono">9:41</span>
      <div class="flex items-center space-x-1">
        <svg width="18" height="18" fill="none"><rect x="1" y="7" width="2" height="6" rx="1" fill="#222"/><rect x="5" y="5" width="2" height="8" rx="1" fill="#222"/><rect x="9" y="3" width="2" height="10" rx="1" fill="#222"/><rect x="13" y="1" width="2" height="12" rx="1" fill="#222"/></svg>
        <svg width="18" height="18" fill="none"><circle cx="9" cy="9" r="7" stroke="#222" stroke-width="2"/></svg>
      </div>
    </div>
    <!-- 内容区 -->
    <div class="flex flex-col items-center px-6 pt-6 pb-4 h-[768px]">
      <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=375&q=80" alt="云梦文化" class="rounded-2xl w-28 h-28 object-cover mb-4 shadow-lg">
      <h1 class="text-2xl font-bold text-gray-800 mb-2">云梦古今法律问答</h1>
      <p class="text-gray-500 text-center mb-4">穿越千年，体验秦简律法与现代法律的碰撞。每次答题5题，积分可上榜，快来挑战吧！</p>
      <div class="flex w-full space-x-3 mb-6">
        <button class="flex-1 py-3 bg-gradient-to-r from-green-400 to-blue-400 text-white rounded-xl text-lg font-semibold shadow-md transition hover:scale-105">开始答题</button>
        <button class="flex-1 py-3 bg-white text-blue-500 border border-blue-400 rounded-xl text-lg font-semibold shadow-md transition hover:scale-105">排行榜</button>
      </div>
      <button class="w-full py-3 bg-white text-green-500 border border-green-400 rounded-xl text-lg font-semibold shadow-md mb-4">历史成绩</button>
      <button class="w-full py-3 bg-white text-gray-500 border border-gray-300 rounded-xl text-base font-medium shadow-sm mb-2">活动规则</button>
      <button class="w-full py-3 bg-white text-blue-400 border border-blue-200 rounded-xl text-base font-medium shadow-sm">分享活动到微信/朋友圈</button>
      <div class="mt-auto flex justify-center w-full">
        <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=80&q=80" alt="青铜器" class="w-12 h-12 rounded-full object-cover border-2 border-green-300 shadow-md mr-2">
        <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=80&q=80" alt="竹简" class="w-12 h-12 rounded-full object-cover border-2 border-blue-300 shadow-md">
      </div>
      <div class="text-xs text-gray-400 mt-2">云梦县文化元素点缀</div>
    </div>
  </div>
</body>
</html> 