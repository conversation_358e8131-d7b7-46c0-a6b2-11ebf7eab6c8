<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>云梦古今法律问答 - 答题</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    html, body { width: 375px; height: 812px; margin: 0; padding: 0; background: #f6f6f6; overflow: hidden; }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 375px; height: 812px; border-radius: 40px; border: 8px solid #000; box-sizing: border-box; margin: 0 auto; background: #f6f6f6; }
    .statusbar { height: 44px; background: transparent; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; }
    .option-selected { @apply bg-gradient-to-r from-green-400 to-blue-400 text-white; }
  </style>
</head>
<body>
  <div class="mockup relative">
    <!-- 状态栏 -->
    <div class="statusbar flex items-center justify-between">
      <span class="text-xs text-gray-700 font-mono">9:41</span>
      <div class="flex items-center space-x-1">
        <svg width="18" height="18" fill="none"><rect x="1" y="7" width="2" height="6" rx="1" fill="#222"/><rect x="5" y="5" width="2" height="8" rx="1" fill="#222"/><rect x="9" y="3" width="2" height="10" rx="1" fill="#222"/><rect x="13" y="1" width="2" height="12" rx="1" fill="#222"/></svg>
        <svg width="18" height="18" fill="none"><circle cx="9" cy="9" r="7" stroke="#222" stroke-width="2"/></svg>
      </div>
    </div>
    <!-- 内容区 -->
    <div class="flex flex-col px-6 pt-4 pb-4 h-[768px]">
      <!-- 进度条 -->
      <div class="flex items-center mb-4">
        <div class="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
          <div class="h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full" style="width: 40%;"></div>
        </div>
        <span class="text-xs text-gray-500">2/5</span>
      </div>
      <!-- 题干 -->
      <div class="bg-white rounded-2xl shadow-md p-4 mb-4">
        <div class="text-xs text-green-500 mb-1">秦简律法原文</div>
        <div class="text-gray-800 text-base font-semibold mb-2">“盗者，斩左趾。”</div>
        <div class="text-xs text-blue-500 mb-1">现代法律条文</div>
        <div class="text-gray-700 text-sm mb-2">《刑法》第二百六十四条：盗窃公私财物，数额较大的，处三年以下有期徒刑、拘役或者管制，并处或者单处罚金。</div>
        <div class="text-gray-500 text-xs">请选择下列哪项最符合两者的共同点：</div>
      </div>
      <!-- 选项 -->
      <div class="flex flex-col space-y-3 mb-6">
        <button class="w-full py-3 bg-white rounded-xl text-gray-800 text-base font-medium shadow-sm border border-gray-200 hover:bg-blue-50 transition">A. 都对盗窃行为进行惩罚</button>
        <button class="w-full py-3 bg-white rounded-xl text-gray-800 text-base font-medium shadow-sm border border-gray-200 hover:bg-blue-50 transition">B. 都采用了死刑</button>
        <button class="w-full py-3 bg-white rounded-xl text-gray-800 text-base font-medium shadow-sm border border-gray-200 hover:bg-blue-50 transition">C. 都只罚款</button>
        <button class="w-full py-3 bg-white rounded-xl text-gray-800 text-base font-medium shadow-sm border border-gray-200 hover:bg-blue-50 transition">D. 都不惩罚</button>
      </div>
      <!-- 下一题按钮 -->
      <button class="w-full py-3 bg-gradient-to-r from-green-400 to-blue-400 text-white rounded-xl text-lg font-semibold shadow-md transition hover:scale-105">下一题</button>
      <div class="mt-auto flex justify-center w-full">
        <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=80&q=80" alt="竹简" class="w-10 h-10 rounded-full object-cover border-2 border-blue-300 shadow-md">
      </div>
    </div>
  </div>
</body>
</html> 