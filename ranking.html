<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>云梦古今法律问答 - 排行榜</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    html, body { width: 375px; height: 812px; margin: 0; padding: 0; background: #f6f6f6; overflow: hidden; }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 375px; height: 812px; border-radius: 40px; border: 8px solid #000; box-sizing: border-box; margin: 0 auto; background: #f6f6f6; }
    .statusbar { height: 44px; background: transparent; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; }
  </style>
</head>
<body>
  <div class="mockup relative">
    <!-- 状态栏 -->
    <div class="statusbar flex items-center justify-between">
      <span class="text-xs text-gray-700 font-mono">9:41</span>
      <div class="flex items-center space-x-1">
        <svg width="18" height="18" fill="none"><rect x="1" y="7" width="2" height="6" rx="1" fill="#222"/><rect x="5" y="5" width="2" height="8" rx="1" fill="#222"/><rect x="9" y="3" width="2" height="10" rx="1" fill="#222"/><rect x="13" y="1" width="2" height="12" rx="1" fill="#222"/></svg>
        <svg width="18" height="18" fill="none"><circle cx="9" cy="9" r="7" stroke="#222" stroke-width="2"/></svg>
      </div>
    </div>
    <!-- 内容区 -->
    <div class="flex flex-col px-6 pt-4 pb-4 h-[768px]">
      <h2 class="text-xl font-bold text-gray-800 mb-4 text-center">积分排行榜</h2>
      <div class="bg-white rounded-2xl shadow p-4 mb-4">
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-lg font-bold text-yellow-400">1</span>
          <img src="https://randomuser.me/api/portraits/men/32.jpg" class="w-10 h-10 rounded-full border-2 border-yellow-400 mr-2" alt="头像">
          <span class="flex-1 text-gray-800 font-semibold">张三</span>
          <span class="text-green-500 font-bold">98分</span>
        </div>
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-lg font-bold text-gray-400">2</span>
          <img src="https://randomuser.me/api/portraits/women/44.jpg" class="w-10 h-10 rounded-full border-2 border-gray-400 mr-2" alt="头像">
          <span class="flex-1 text-gray-800 font-semibold">李四</span>
          <span class="text-green-500 font-bold">95分</span>
        </div>
        <div class="flex items-center mb-3">
          <span class="w-8 text-center text-lg font-bold text-orange-400">3</span>
          <img src="https://randomuser.me/api/portraits/men/65.jpg" class="w-10 h-10 rounded-full border-2 border-orange-400 mr-2" alt="头像">
          <span class="flex-1 text-gray-800 font-semibold">王五</span>
          <span class="text-green-500 font-bold">92分</span>
        </div>
        <!-- ...更多用户... -->
      </div>
      <button class="w-full py-3 bg-gradient-to-r from-green-400 to-blue-400 text-white rounded-xl text-lg font-semibold shadow-md mb-4">刷新排行榜</button>
      <div class="mt-auto flex justify-center w-full">
        <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=80&q=80" alt="竹简" class="w-10 h-10 rounded-full object-cover border-2 border-blue-300 shadow-md">
      </div>
    </div>
  </div>
</body>
</html> 