<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=375, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>云梦古今法律问答 - 活动规则</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    html, body { width: 375px; height: 812px; margin: 0; padding: 0; background: #f6f6f6; overflow: hidden; }
    ::-webkit-scrollbar { display: none; }
    .mockup { width: 375px; height: 812px; border-radius: 40px; border: 8px solid #000; box-sizing: border-box; margin: 0 auto; background: #f6f6f6; }
    .statusbar { height: 44px; background: transparent; display: flex; align-items: center; justify-content: space-between; padding: 0 16px; }
  </style>
</head>
<body>
  <div class="mockup relative">
    <!-- 状态栏 -->
    <div class="statusbar flex items-center justify-between">
      <span class="text-xs text-gray-700 font-mono">9:41</span>
      <div class="flex items-center space-x-1">
        <svg width="18" height="18" fill="none"><rect x="1" y="7" width="2" height="6" rx="1" fill="#222"/><rect x="5" y="5" width="2" height="8" rx="1" fill="#222"/><rect x="9" y="3" width="2" height="10" rx="1" fill="#222"/><rect x="13" y="1" width="2" height="12" rx="1" fill="#222"/></svg>
        <svg width="18" height="18" fill="none"><circle cx="9" cy="9" r="7" stroke="#222" stroke-width="2"/></svg>
      </div>
    </div>
    <!-- 内容区 -->
    <div class="flex flex-col px-6 pt-4 pb-4 h-[768px]">
      <h2 class="text-xl font-bold text-gray-800 mb-4 text-center">活动规则</h2>
      <div class="bg-white rounded-2xl shadow p-4 mb-4 text-gray-700 text-base leading-relaxed">
        <ol class="list-decimal pl-4 space-y-2">
          <li>每次答题共5题，全部为选择题，题目内容涵盖秦代律法与现今中国法律的对比。</li>
          <li>每题展示秦简律法原文、现代法律条文及选项，答题后可查看正确答案与解析。</li>
          <li>每次答题结束后可获得积分，积分可累计并参与排行榜。</li>
          <li>排行榜展示用户头像、昵称及积分排名。</li>
          <li>支持一键分享到微信好友、朋友圈。</li>
          <li>页面设计融入云梦县本地文化元素，体验古今法治智慧。</li>
        </ol>
      </div>
      <button class="w-full py-3 bg-gradient-to-r from-green-400 to-blue-400 text-white rounded-xl text-lg font-semibold shadow-md">返回首页</button>
      <div class="mt-auto flex justify-center w-full">
        <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=80&q=80" alt="竹简" class="w-10 h-10 rounded-full object-cover border-2 border-blue-300 shadow-md">
      </div>
    </div>
  </div>
</body>
</html> 