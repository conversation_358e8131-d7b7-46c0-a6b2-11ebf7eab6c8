# 云梦古今法律问答小游戏 产品设计文档

## 一、产品定位

本产品为"云梦一码游"小程序内的专题活动，面向学生和政府人员，通过选择题形式对比秦代律法与现今中国法律，寓教于乐，弘扬法治精神，传播云梦本地文化。

## 二、核心功能

1. **用户登录**  
   - 支持微信授权登录，未登录不可参与答题。

2. **法律知识问答**  
   - 每次答题5题，全部为选择题。
   - 题目内容由系统编辑，涵盖秦代律法与现今法律的对比。
   - 题库共20题，系统随机抽取。
   - 每题展示秦简律法原文、现代法律条文及选项。
   - 答题结束后展示正确答案与详细解析。

3. **积分与排行榜**  
   - 答题获得积分，积分可累计。
   - 展示全站排行榜，激励用户参与。
   - 排行榜展示用户头像、昵称等社交元素。

4. **分享功能**  
   - 支持一键分享到微信好友、朋友圈。

5. **本地文化融入**  
   - 页面设计融入云梦县文化元素（如青铜器、竹简、云梦地图等）。
   - 视觉风格参考 [Figma设计稿](https://www.figma.com/design/NgvvQ3c2Of80nmEzgHn5X3/Tasks-Planner----Mobile--Community-?node-id=82-312&p=f&t=PTohL0PYhqgF9HsR-0)。

6. **辅助功能**  
   - 支持查看历史答题记录。
   - 支持查看题目解析。

## 三、用户流程

1. 用户进入活动页面，微信授权登录。
2. 进入答题首页，点击"开始答题"。
3. 系统随机抽取5道题，逐题作答。
4. 答题结束，展示成绩、正确答案与解析。
5. 用户可选择分享成绩到微信/朋友圈。
6. 查看排行榜，了解自己排名。
7. 可随时查看历史答题记录与解析。

## 四、页面风格与设计规范

- 参考Figma链接风格，整体清新、优雅、留白充足。
- 采用云梦县本地文化元素点缀。
- 设计图尺寸375x812PX，带黑色mock up。
- 图标引用在线矢量图标库。
- 图片使用Unsplash开源图片。
- 样式全部通过TailwindCSS CDN引入。
- 状态栏与标题栏背景色一致（可透明）。
- 底部tab栏为白色填充，100%不透明度。
- 强制隐藏所有滚动条。

## 五、技术要求

- 支持移动端适配。
- 支持微信小程序分享API。
- 无需后台管理，题库与积分等数据前端维护。

---

> 产品功能设计已完成，进入交互设计阶段。 