# 需求文档

## 引言
荆门市"医保数据联动应用信息系统"是一个面向医保局与其他政府机关、医药机构进行工单联动处理的平台。系统支持7个核心业务场景，包括死亡人员数据核查、意外伤害报销核查、住院病患外出核查、病患投诉举报核查、执业药师在岗核查、职工医保征缴投诉和移送问题线索。系统通过标准化的工单流转机制，实现跨部门数据核查与问题处理，提高医保监管效率和数据准确性。

# 需求

### 需求1：死亡人员数据核查工单管理
**用户故事:** 作为市医保局工作人员，我想要发起死亡人员数据核查工单，以便县医保局能够及时核实并处理死亡人员医保数据异常问题。

#### 验收标准
1. 当大数据局定期上传死亡人员数据时，那么系统应自动创建待处理的死亡人员核查工单
2. 当市医保局工作人员选择"死亡人员数据核查"工单类型时，那么系统应显示标准化的死亡人员核查表单
3. 当市医保局提交死亡人员核查工单时，那么系统应将工单分配给指定的县医保局
4. 当县医保局登录系统时，那么系统应显示待处理的死亡人员核查工单列表
5. 当县医保局完成核查并提交处理结果时，那么系统应更新工单状态为"处理完毕"
6. 当市医保局确认处理结果后，那么系统应将工单状态更新为"已归档"

#### 序列图
```mermaid
sequenceDiagram
    participant 大数据局
    participant 市医保局
    participant 系统
    participant 县医保局

    大数据局->>系统: 定期上传死亡人员数据
    系统-->>市医保局: 数据接收成功通知
    市医保局->>系统: 创建死亡人员核查工单
    系统->>系统: 生成工单编号，状态设为"待处理"
    系统->>县医保局: 分派工单通知
    县医保局->>系统: 查看工单详情
    县医保局->>系统: 提交处理结果和证明材料
    系统->>系统: 更新状态为"处理完毕"
    系统-->>市医保局: 处理完成通知
    市医保局->>系统: 查看并确认处理结果
    系统->>系统: 更新状态为"已归档"
```

### 需求2：意外伤害报销数据核查工单管理
**用户故事:** 作为市医保局工作人员，我想要发起意外伤害报销数据核查工单，以便公安、法院、检察院等执法机关能够核实意外伤害医保报销的合法性。

#### 验收标准
1. 当市医保局选择"意外伤害报销核查"工单类型时，那么系统应显示意外伤害报销核查专用表单
2. 当市医保局上传意外伤害报销相关附件时，那么系统应支持Excel、Word、PDF和图片格式文件
3. 当市医保局提交意外伤害核查工单时，那么系统应将工单发送给指定的执法机关
4. 当执法机关登录系统时，那么系统应显示待处理的意外伤害核查工单
5. 当执法机关提交处理结果时，那么系统应允许上传案件核实证明等附件
6. 当处理结果不符合要求时，那么系统应允许市医保局要求承办方补充办理

#### 序列图
```mermaid
sequenceDiagram
    participant 市医保局
    participant 系统
    participant 执法机关

    市医保局->>系统: 选择意外伤害报销核查类型
    市医保局->>系统: 填写表单并上传附件
    市医保局->>系统: 选择执法机关作为承办方
    市医保局->>系统: 提交工单
    系统->>系统: 生成工单编号，状态设为"待处理"
    系统->>执法机关: 发送工单通知
    执法机关->>系统: 查看工单详情和附件
    执法机关->>系统: 进行案件核实
    执法机关->>系统: 上传核实结果和证明材料
    系统->>系统: 更新状态为"处理完毕"
    系统-->>市医保局: 处理完成通知
    alt 结果不符合要求
        市医保局->>系统: 要求补充办理
        系统->>执法机关: 补充办理通知
        执法机关->>系统: 重新提交结果
    end
```

### 需求3：住院病患外出信息核查工单管理
**用户故事:** 作为市医保局工作人员，我想要发起住院病患异常外出核实工单，以便公安部门能够通过"天网"系统核实住院人员外出情况。

#### 验收标准
1. 当市医保局选择"住院病患外出核查"工单类型时，那么系统应显示住院病患外出核查表单
2. 当市医保局提交住院病患外出核查工单时，那么系统应将工单发送给公安部门
3. 当公安部门收到核查工单时，那么系统应提供住院人员基本信息和住院时间段
4. 当公安部门通过"天网"系统完成外出行为核实后，那么系统应允许上传核实结果和相关证据
5. 当核实结果显示存在异常外出时，那么系统应标记该工单为异常并通知市医保局
6. 当核实过程需要补充信息时，那么系统应支持承办方与发起方的信息交互

#### 序列图
```mermaid
sequenceDiagram
    participant 市医保局
    participant 系统
    participant 公安部门

    市医保局->>系统: 选择住院病患外出核查类型
    市医保局->>系统: 填写病患信息和住院时间段
    市医保局->>系统: 选择公安部门作为承办方
    市医保局->>系统: 提交工单
    系统->>系统: 生成工单编号，状态设为"待处理"
    系统->>公安部门: 发送核查通知
    公安部门->>系统: 查看病患信息和住院时间段
    公安部门->>公安部门: 通过"天网"系统核实外出情况
    alt 发现异常外出
        公安部门->>系统: 上传异常证据和处理结果
        系统->>系统: 标记工单为异常状态
        系统-->>市医保局: 异常外出通知
    else 无异常外出
        公安部门->>系统: 上传无异常证明
        系统-->>市医保局: 核实完成通知
    end
    系统->>系统: 更新状态为"处理完毕"
```

### 需求4：病患投诉举报信息核查工单管理
**用户故事:** 作为市医保局工作人员，我想要发起病患投诉举报信息核查工单，以便相关医院能够及时核实并处理患者投诉举报。

#### 验收标准
1. 当市医保局选择"病患投诉举报核查"工单类型时，那么系统应显示病患投诉举报核查表单
2. 当市医保局填写投诉举报内容时，那么系统应提供富文本编辑器支持详细描述
3. 当市医保局提交投诉举报核查工单时，那么系统应将工单发送给被投诉的医院
4. 当医院登录系统时，那么系统应显示待处理的投诉举报工单列表
5. 当医院处理投诉举报并提交结果时，那么系统应允许上传处理报告和相关证明材料
6. 当医院无法在规定时间内处理时，那么系统应支持申请延期处理功能

#### 序列图
```mermaid
sequenceDiagram
    participant 市医保局
    participant 系统
    participant 医院

    市医保局->>系统: 选择病患投诉举报核查类型
    市医保局->>系统: 填写投诉信息和患者信息
    市医保局->>系统: 选择被投诉医院作为承办方
    市医保局->>系统: 提交工单
    系统->>系统: 自动匹配医院信息，状态设为"待处理"
    系统->>医院: 发送投诉核查通知
    医院->>系统: 查看投诉详情和患者信息
    医院->>医院: 内部核实投诉情况
    医院->>系统: 上传处理结果和证明材料
    alt 需要补充信息
        医院->>系统: 发起补充信息请求
        系统-->>市医保局: 补充信息通知
        市医保局->>系统: 补充信息
        系统->>医院: 补充信息推送
    end
    系统->>系统: 更新状态为"处理完毕"
    系统-->>市医保局: 处理完成通知
```

### 需求5：执业药师在岗情况核查工单管理
**用户故事:** 作为市医保局工作人员，我想要发起执业药师在岗情况核查工单，以便相关药店能够证明药师在岗情况并上传佐证材料。

#### 验收标准
1. 当市医保局选择"执业药师在岗核查"工单类型时，那么系统应显示标准化的执业药师核查表单
2. 当市医保局提交执业药师核查工单时，那么系统应将工单发送给相关药店
3. 当药店收到核查工单时，那么系统应明确显示需要证明的药师信息和在岗要求
4. 当药店上传佐证材料时，那么系统应支持劳务合同、社保缴费证明等文件格式
5. 当药店提交在岗证明材料后，那么系统应自动验证材料的完整性和格式正确性
6. 当证明材料不符合要求时，那么系统应通知药店补充或重新上传

#### 序列图
```mermaid
sequenceDiagram
    participant 市医保局
    participant 系统
    participant 药店

    市医保局->>系统: 选择执业药师在岗核查类型
    市医保局->>系统: 填写药店和药师信息
    市医保局->>系统: 选择被核查药店作为承办方
    市医保局->>系统: 提交工单
    系统->>系统: 生成工单编号，状态设为"待处理"
    系统->>药店: 发送在岗核查通知
    药店->>系统: 查看核查要求和佐证清单
    药店->>药店: 准备在岗证明材料
    药店->>系统: 上传劳务合同、社保缴费证明等佐证
    系统->>系统: 自动验证材料完整性和格式
    alt 材料验证不通过
        系统-->>药店: 补充或重新上传通知
        药店->>系统: 重新上传证明材料
    else 材料验证通过
        系统->>系统: 更新状态为"处理完毕"
        系统-->>市医保局: 处理完成通知
    end
```

### 需求6：职工医保征缴投诉案件处理工单管理
**用户故事:** 作为市医保局工作人员，我想要发起职工医保征缴投诉案件处理工单，以便税务部门能够完成企业医保催缴工作。

#### 验收标准
1. 当市医保局选择"职工医保征缴投诉"工单类型时，那么系统应显示职工医保征缴投诉表单
2. 当市医保局填写投诉信息时，那么系统应自动带入职工应参保登记和缴费基数信息
3. 当市医保局提交征缴投诉工单时，那么系统应将工单发送给税务部门
4. 当税务部门收到征缴工单时，那么系统应显示被投诉企业的详细信息和欠费情况
5. 当税务部门完成催缴后，那么系统应允许上传催缴结果和缴费凭证
6. 当企业完成缴费后，那么系统应自动更新工单状态并通知市医保局

#### 序列图
```mermaid
sequenceDiagram
    participant 市医保局
    participant 系统
    participant 税务部门

    市医保局->>系统: 选择职工医保征缴投诉类型
    市医保局->>系统: 填写企业信息和投诉详情
    市医保局->>系统: 选择税务部门作为承办方
    市医保局->>系统: 提交工单
    系统->>系统: 生成工单编号，状态设为"待处理"
    系统->>税务部门: 发送征缴投诉通知
    税务部门->>系统: 查看企业信息和投诉详情
    税务部门->>税务部门: 执行医保催缴工作
    alt 催缴成功
        税务部门->>系统: 上传催缴结果和补缴证明
        系统->>系统: 更新状态为"处理完成"
        系统-->>市医保局: 催缴完成通知
    else 催缴遇到困难
        税务部门->>系统: 发起协调请求
        系统-->>市医保局: 协调通知
        市医保局->>系统: 提供协调支持
        系统->>税务部门: 协调信息推送
        税务部门->>系统: 继续催缴工作
    end
```

### 需求7：移送问题线索工单管理
**用户故事:** 作为政府机关工作人员，我想要发起移送问题线索工单，以便承办方能够及时处理并回复处理结果。

#### 验收标准
1. 当发起方选择"移送问题线索"工单类型时，那么系统应显示问题线索移送表单
2. 当发起方填写问题线索时，那么系统应提供富文本编辑器支持详细描述问题
3. 当发起方上传相关附件时，那么系统应支持多种文件格式并限制单个文件大小不超过10MB
4. 当发起方提交移送工单时，那么系统应将工单发送给指定的承办方
5. 当承办方收到移送工单时，那么系统应显示完整的问题线索和附件信息
6. 当承办方完成处理后，那么系统应要求提交处理结果和相关证明材料

#### 序列图
```mermaid
sequenceDiagram
    participant 政府机关
    participant 系统
    participant 承办方

    政府机关->>系统: 选择移送问题线索类型
    政府机关->>系统: 填写线索信息和处理要求
    政府机关->>系统: 选择承办方并验证权限
    政府机关->>系统: 提交工单
    系统->>系统: 生成唯一线索编号，状态设为"待处理"
    系统->>承办方: 发送问题线索移送通知
    承办方->>系统: 查看线索详情和附件
    承办方->>承办方: 开展线索调查工作
    承办方->>系统: 上传处理结果和相关证明材料
    系统->>系统: 更新状态为"处理完毕"
    系统-->>政府机关: 处理完成通知
    alt 需要复核
        政府机关->>系统: 要求补充说明
        系统->>承办方: 补充说明通知
        承办方->>系统: 重新提交处理结果
        系统-->>政府机关: 重新提交通知
    end
```

### 需求8：工单创建与提交功能
**用户故事:** 作为系统用户，我想要创建和提交工单，以便启动跨部门的数据核查和问题处理流程。

#### 验收标准
1. 当用户点击"新建工单"按钮时，那么系统应显示工单创建表单
2. 当用户选择工单类型时，那么系统应根据类型显示对应的表单字段
3. 当用户填写工单编号时，那么系统应自动检查编号的唯一性
4. 当用户填写工单标题时，那么系统应限制标题长度不超过100个字符
5. 当用户使用富文本编辑器时，那么系统应支持文本格式化、图片插入和表格编辑
6. 当用户上传附件时，那么系统应支持Excel、Word、PDF、图片格式并显示上传进度
7. 当用户选择承办方时，那么系统应显示可选的承办机构列表
8. 当用户点击"提交并复制工单"时，那么系统应提交当前工单并创建内容相同的新工单
9. 当用户提交工单时，那么系统应验证所有必填字段的完整性

#### 序列图
```mermaid
sequenceDiagram
    participant 用户
    participant 系统
    participant 承办方

    用户->>系统: 点击"新建工单"
    系统-->>用户: 显示工单创建表单
    用户->>系统: 选择工单类型
    系统-->>用户: 显示对应表单字段
    用户->>系统: 填写工单信息
    系统->>系统: 验证必填项及编号唯一性
    用户->>系统: 上传附件
    系统->>系统: 验证格式与大小
    用户->>系统: 选择承办方
    系统-->>用户: 显示可选承办机构列表
    用户->>系统: 提交工单
    系统->>系统: 生成唯一工单编号
    系统->>系统: 设置状态为"待处理"
    系统->>承办方: 发送工单通知
    系统-->>用户: 提交成功确认
```

### 需求9：工单处理与反馈功能
**用户故事:** 作为承办方用户，我想要处理收到的工单并反馈结果，以便完成数据核查和问题处理。

#### 验收标准
1. 当承办方登录系统时，那么系统应显示"待处理"状态的工单列表
2. 当承办方点击工单时，那么系统应显示原始工单的完整只读信息
3. 当承办方处理工单时，那么系统应提供富文本编辑器填写处理结果
4. 当承办方上传处理结果附件时，那么系统应支持相同的文件格式限制
5. 当承办方提交处理结果时，那么系统应更新工单状态为"处理完毕"
6. 当发起方要求补充办理结果时，那么系统应将工单状态重置为"待处理"
7. 当处理结果提交后，那么系统应自动通知发起方有新的处理结果

#### 序列图
```mermaid
sequenceDiagram
    participant 承办方
    participant 系统
    participant 发起方

    承办方->>系统: 登录系统
    系统-->>承办方: 显示待处理工单列表
    承办方->>系统: 选择工单查看详情
    系统-->>承办方: 显示完整工单信息和附件
    承办方->>系统: 开始处理工单
    系统->>系统: 更新状态为"处理中"
    承办方->>系统: 录入处理结果
    承办方->>系统: 上传处理结果附件
    系统->>系统: 验证附件格式和完整性
    承办方->>系统: 提交处理结果
    系统->>系统: 更新状态为"处理完毕"
    系统-->>发起方: 处理完成通知
    发起方->>系统: 查看处理结果和附件
```

### 需求10：工单状态管理
**用户故事:** 作为系统用户，我想要跟踪和管理工单状态，以便了解工单处理的进展情况。

#### 验收标准
1. 当工单创建时，那么系统应将初始状态设置为"待处理"
2. 当承办方提交处理结果时，那么系统应将状态更新为"处理完毕"
3. 当发起方确认处理结果后，那么系统应将状态更新为"已归档"
4. 当发起方撤回工单时，那么系统应将状态更新为"已撤回"
5. 当用户查看工单列表时，那么系统应显示每个工单的当前状态
6. 当用户点击状态筛选时，那么系统应显示对应状态的工单列表
7. 当工单状态变更时，那么系统应记录状态变更时间和操作人员信息

#### 序列图
```mermaid
sequenceDiagram
    participant 用户
    participant 系统
    participant 通知服务

    用户->>系统: 查看工单列表
    系统-->>用户: 显示工单状态概览
    用户->>系统: 选择具体工单
    系统-->>用户: 显示当前状态和历史变化
    
    rect rgb(240, 240, 240)
        Note over 系统: 状态自动管理
        系统->>系统: 监控工单处理时间
        系统->>通知服务: 超时提醒触发
        通知服务-->>用户: 发送超时提醒
        系统->>系统: 更新状态为"超时"
    end
    
    用户->>系统: 按状态筛选工单
    系统-->>用户: 返回筛选结果列表
    
    alt 工单重新处理
        用户->>系统: 申请状态回退
        系统->>系统: 验证回退权限
        系统->>系统: 状态回退到指定节点
        系统-->>用户: 状态更新确认
    end
```

### 需求11：用户权限与访问控制
**用户故事:** 作为系统管理员，我想要管理不同用户的访问权限，以便确保数据安全和操作合规。

#### 验收标准
1. 当用户登录系统时，那么系统应根据用户所属部门显示对应的功能菜单
2. 当市医保局用户登录时，那么系统应显示所有工单类型的创建权限
3. 当县医保局用户登录时，那么系统应仅显示死亡人员核查的处理权限
4. 当公安部门用户登录时，那么系统应显示意外伤害核查和住院病患外出核查的处理权限
5. 当医院用户登录时，那么系统应仅显示病患投诉举报核查的处理权限
6. 当药店用户登录时，那么系统应仅显示执业药师在岗核查的处理权限
7. 当税务部门用户登录时，那么系统应仅显示职工医保征缴投诉的处理权限
8. 当用户尝试越权操作时，那么系统应拒绝访问并记录操作日志

#### 序列图
```mermaid
sequenceDiagram
    participant 管理员
    participant 系统
    participant 用户
    participant 审计日志

    管理员->>系统: 创建新用户账户
    管理员->>系统: 分配角色和权限
    系统->>系统: 验证权限配置合法性
    系统-->>管理员: 创建成功确认
    
    用户->>系统: 登录系统
    系统->>系统: 验证用户身份
    系统->>系统: 查询用户角色和权限
    系统-->>用户: 显示对应功能菜单
    
    用户->>系统: 尝试访问功能
    系统->>系统: 验证用户权限
    alt 权限验证通过
        系统-->>用户: 允许访问功能
        用户->>系统: 执行敏感操作
        系统->>审计日志: 记录操作详情
    else 权限验证失败
        系统-->>用户: 拒绝访问提示
        系统->>审计日志: 记录拒绝访问日志
    end
    
    管理员->>系统: 修改用户权限
    系统->>系统: 更新权限配置
    系统-->>管理员: 权限更新确认
    系统->>审计日志: 记录权限变更日志
```

### 需求12：系统通知与消息提醒
**用户故事:** 作为系统用户，我想要及时收到工单相关的通知提醒，以便及时处理待办事项。

#### 验收标准
1. 当有新工单分配给承办方时，那么系统应向承办方发送即时通知
2. 当工单状态变更时，那么系统应向相关用户发送状态变更通知
3. 当承办方提交处理结果时，那么系统应向发起方发送处理完成通知
4. 当用户登录系统时，那么系统应显示未读通知的数量
5. 当用户点击通知时，那么系统应跳转到对应的工单详情页面
6. 当通知超过7天未读时，那么系统应自动标记为已读状态
7. 当系统发送通知失败时，那么系统应重试发送并记录失败日志

#### 序列图
```mermaid
sequenceDiagram
    participant 系统
    participant 通知服务
    participant 用户
    participant 消息日志

    rect rgb(240, 240, 240)
        Note over 系统: 工单分配通知
        系统->>通知服务: 触发分配通知
        通知服务->>用户: 发送工单分配通知
        通知服务->>消息日志: 记录通知发送日志
    end

    rect rgb(240, 240, 240)
        Note over 系统: 状态变更通知
        系统->>通知服务: 触发状态变更通知
        通知服务->>用户: 发送状态变更通知
        通知服务->>消息日志: 记录通知发送日志
    end

    rect rgb(240, 240, 240)
        Note over 系统: 超时提醒通知
        系统->>系统: 监控工单处理时间
        系统->>通知服务: 触发超时提醒
        通知服务->>用户: 发送超时提醒通知
    end

    用户->>系统: 点击通知链接
    系统-->>用户: 跳转到对应工单详情
    用户->>系统: 查看通知详情
    系统->>系统: 标记通知为已读
    系统->>消息日志: 记录通知查看日志

    alt 通知发送失败
        通知服务->>系统: 返回发送失败
        系统->>消息日志: 记录失败原因
        系统->>通知服务: 重试发送通知
    end
```